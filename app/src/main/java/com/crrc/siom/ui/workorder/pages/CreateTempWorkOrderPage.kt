package com.crrc.siom.ui.workorder.pages

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.bean.response.TempWorkOrderParamResponse
import com.crrc.siom.ui.workorder.viewmodel.CreateTempWorkOrderViewModel
import androidx.compose.ui.tooling.preview.Preview

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateTempWorkOrderPage(
    onBackClick: () -> Unit,
    viewModel: CreateTempWorkOrderViewModel = viewModel(),
    onSubmit: (optionParam: TempWorkOrderParamResponse?) -> Unit = {},
) {
    val optionParam by viewModel.optionParam.collectAsState()
    CreateTempWorkOrderPageContent(
        onBackClick = onBackClick,
        optionParam = optionParam,
        onSubmit = onSubmit
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateTempWorkOrderPageContent(
    onBackClick: () -> Unit,
    optionParam: TempWorkOrderParamResponse?,
    onSubmit: (optionParam: TempWorkOrderParamResponse?) -> Unit = {},
) {
    val scrollState = rememberScrollState()

    // 使用 remember 和 mutableStateOf 来管理选择状态，确保 UI 更新
    var typeOptions by remember { mutableStateOf(optionParam?.type ?: emptyList()) }
    var lineOptions by remember { mutableStateOf(optionParam?.line ?: emptyList()) }
    var stationOptions by remember { mutableStateOf(optionParam?.station ?: emptyList()) }
    var groupOptions by remember { mutableStateOf(optionParam?.group ?: emptyList()) }
    val personOptions = groupOptions.flatMap { it.members ?: emptyList() }
    var priorityOptions by remember { mutableStateOf(optionParam?.priority ?: emptyList()) }
    var standbyOptions by remember { mutableStateOf(optionParam?.standby ?: emptyList()) }
    var toolOptions by remember { mutableStateOf(optionParam?.tool ?: emptyList()) }
    var description by remember { mutableStateOf(optionParam?.description ?: "") }
    var instructions by remember { mutableStateOf(optionParam?.maintenanceGuide ?: "") }

    // 当 optionParam 变化时更新状态
    LaunchedEffect(optionParam) {
        optionParam?.let { param ->
            typeOptions = param.type ?: emptyList()
            lineOptions = param.line ?: emptyList()
            stationOptions = param.station ?: emptyList()
            groupOptions = param.group ?: emptyList()
            priorityOptions = param.priority ?: emptyList()
            standbyOptions = param.standby ?: emptyList()
            toolOptions = param.tool ?: emptyList()
            description = param.description ?: ""
            instructions = param.maintenanceGuide ?: ""
        }
    }

    Column(modifier = Modifier.fillMaxSize()) {
        TopAppBar(
            title = { Text("新建工单") },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            }
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            // 工单基本信息
            FormRowSingleSelect(
                label = "工单类型",
                options = typeOptions,
                onSelect = { selected ->
                    typeOptions.forEach { it.selected = false }
                    selected.selected = true
                }
            )
            FormRowSingleSelect(
                label = "所属线路",
                options = lineOptions,
                onSelect = { selected ->
                    lineOptions.forEach { it.selected = false }
                    selected.selected = true
                }
            )
            FormRowSingleSelect(
                label = "所属车站",
                options = stationOptions,
                onSelect = { selected ->
                    stationOptions.forEach { it.selected = false }
                    selected.selected = true
                }
            )
            FormRowGroupSingleSelect(
                label = "所属班组",
                options = groupOptions,
                onSelect = { selected ->
                    groupOptions.forEach { it.isSelected = false }
                    selected.isSelected = true
                }
            )
            // 负责人（单选，来源于所有 group 下 members）
            FormRowSingleSelect(
                label = "负责人",
                options = personOptions,
                onSelect = { selected ->
                    personOptions.forEach { it.selected = false }
                    selected.selected = true
                }
            )
            // 维修人员（单选，来源于所有 group 下 members）
            FormRowSingleSelect(
                label = "维修人员",
                options = personOptions,
                onSelect = { selected ->
                    personOptions.forEach { it.selected = false }
                    selected.selected = true
                }
            )
            // 优先级选择
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "优先级",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.width(80.dp)
                )
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    priorityOptions.forEach { item ->
                        PriorityChip(
                            text = item.name,
                            isSelected = item.selected,
                            onClick = {
                                priorityOptions.forEach { it.selected = false }
                                item.selected = true
                            }
                        )
                    }
                }
            }
            // 备品备件（多选）
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "备品备件",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.width(80.dp)
                )
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = MaterialTheme.colorScheme.surfaceVariant,
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(8.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    standbyOptions.forEach { item ->
                        PartsChip(
                            text = item.name,
                            isSelected = item.selected,
                            onClick = {
                                item.selected = !item.selected
                            }
                        )
                    }
                }
            }
            // 工器具（多选）
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "工器具",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.width(80.dp)
                )
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = MaterialTheme.colorScheme.surfaceVariant,
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(8.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    toolOptions.forEach { item ->
                        PartsChip(
                            text = item.name,
                            isSelected = item.selected,
                            onClick = {
                                item.selected = !item.selected
                            }
                        )
                    }
                }
            }
            // 工单描述
            Column {
                Text(
                    text = "工单描述",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                OutlinedTextField(
                    value = description,
                    onValueChange = {
                        description = it
                        optionParam?.description = it
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    placeholder = { Text("请输入工单描述") }
                )
            }
            // 维修指导
            Column {
                Text(
                    text = "维修指导",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                OutlinedTextField(
                    value = instructions,
                    onValueChange = {
                        instructions = it
                        optionParam?.maintenanceGuide = it
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    placeholder = { Text("请输入维修指导") }
                )
            }
            // 提交按钮
            Button(
                onClick = {
                    onSubmit(optionParam)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp)
            ) {
                Text("提交")
            }
        }
    }
}

@Composable
private fun FormRowSingleSelect(
    label: String,
    options: List<TempWorkOrderParamResponse.Item>,
    onSelect: (TempWorkOrderParamResponse.Item) -> Unit
) {
    val selected = options.find { it.selected }
    var expanded by remember { mutableStateOf(false) }
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.width(80.dp)
        )
        Box(modifier = Modifier.fillMaxWidth()) {
            OutlinedTextField(
                value = selected?.name ?: "",
                onValueChange = {},
                readOnly = true,
                placeholder = { Text("请选择$label") },
                trailingIcon = {
                    Icon(Icons.Default.KeyboardArrowDown, contentDescription = null)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { expanded = true }
            )
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                modifier = Modifier.fillMaxWidth()
            ) {
                options.forEach { item ->
                    DropdownMenuItem(
                        text = { Text(item.name) },
                        onClick = {
                            onSelect(item)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun FormRowGroupSingleSelect(
    label: String,
    options: List<TempWorkOrderParamResponse.Group>,
    onSelect: (TempWorkOrderParamResponse.Group) -> Unit
) {
    val selected = options.find { it.isSelected }
    var expanded by remember { mutableStateOf(false) }
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.width(80.dp)
        )
        Box(modifier = Modifier.fillMaxWidth()) {
            OutlinedTextField(
                value = selected?.name ?: "",
                onValueChange = {},
                readOnly = true,
                placeholder = { Text("请选择$label") },
                trailingIcon = {
                    Icon(Icons.Default.KeyboardArrowDown, contentDescription = null)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { expanded = true }
            )
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                modifier = Modifier.fillMaxWidth()
            ) {
                options.forEach { group ->
                    DropdownMenuItem(
                        text = { Text(group.name) },
                        onClick = {
                            onSelect(group)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun PriorityChip(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier.height(32.dp),
        shape = RoundedCornerShape(16.dp),
        color = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.surfaceVariant,
        onClick = onClick
    ) {
        Box(
            modifier = Modifier.padding(horizontal = 12.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = text,
                color = if (isSelected) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurfaceVariant,
                style = MaterialTheme.typography.bodySmall,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun PartsChip(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier.height(32.dp),
        shape = RoundedCornerShape(16.dp),
        color = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.surfaceVariant,
        onClick = onClick
    ) {
        Box(
            modifier = Modifier.padding(horizontal = 12.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = text,
                color = if (isSelected) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurfaceVariant,
                style = MaterialTheme.typography.bodySmall,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Preview(showBackground = true, name = "CreateTempWorkOrderPage Preview")
@Composable
fun PreviewCreateTempWorkOrderPage() {
    val mockParam = TempWorkOrderParamResponse().apply {
        type = listOf(
            TempWorkOrderParamResponse.Item().apply { id = "1"; name = "类型A"; selected = true },
            TempWorkOrderParamResponse.Item().apply { id = "2"; name = "类型B"; selected = false }
        )
        line = listOf(
            TempWorkOrderParamResponse.Item().apply { id = "1"; name = "1号线"; selected = true },
            TempWorkOrderParamResponse.Item().apply { id = "2"; name = "2号线"; selected = false }
        )
        station = listOf(
            TempWorkOrderParamResponse.Item().apply { id = "1"; name = "站点A"; selected = true },
            TempWorkOrderParamResponse.Item().apply { id = "2"; name = "站点B"; selected = false }
        )
        group = listOf(
            TempWorkOrderParamResponse.Group().apply {
                id = "1"; name = "班组1"; isSelected = true
                members = listOf(
                    TempWorkOrderParamResponse.Item().apply { id = "11"; name = "张三"; selected = true },
                    TempWorkOrderParamResponse.Item().apply { id = "12"; name = "李四"; selected = false }
                )
            },
            TempWorkOrderParamResponse.Group().apply {
                id = "2"; name = "班组2"; isSelected = false
                members = listOf(
                    TempWorkOrderParamResponse.Item().apply { id = "21"; name = "王五"; selected = false }
                )
            }
        )
        priority = listOf(
            TempWorkOrderParamResponse.Item().apply { id = "1"; name = "低"; selected = true },
            TempWorkOrderParamResponse.Item().apply { id = "2"; name = "高"; selected = false }
        )
        standby = listOf(
            TempWorkOrderParamResponse.AmountItem().apply { id = 1; name = "备件A"; amount = 2; selected = true },
            TempWorkOrderParamResponse.AmountItem().apply { id = 2; name = "备件B"; amount = 1; selected = false }
        )
        tool = listOf(
            TempWorkOrderParamResponse.AmountItem().apply { id = 1; name = "工具A"; amount = 1; selected = true },
            TempWorkOrderParamResponse.AmountItem().apply { id = 2; name = "工具B"; amount = 1; selected = false }
        )
        description = "测试工单描述"
        maintenanceGuide = "测试维修指导"
    }
    CreateTempWorkOrderPageContent(
        onBackClick = {},
        optionParam = mockParam
    )
}